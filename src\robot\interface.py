# src/robot/interface.py
"""
统一机器人接口模块

该模块组合各个专用模块，提供统一的机器人控制接口，保持向后兼容性。

作者: Augment Agent
日期: 2025-07-15
"""

from ..core.connection import ConnectionManager
from ..core.error_handler import ErrorHandler
from .base import BaseRobot
from .servo import ServoController
from .motion import MotionController
from .status import StatusMonitor


class RobotInterface:
    """
    统一的机器人接口
    
    组合各个专用模块，提供完整的机器人控制功能，保持向后兼容性。
    """
    
    def __init__(self, ip, port):
        """
        初始化机器人接口
        
        Args:
            ip (str): 机器人IP地址
            port (int): 机器人通信端口
        """
        # 初始化核心组件
        self.conn = ConnectionManager(ip, port)
        self.error_handler = ErrorHandler(self.conn)
        
        # 初始化功能模块
        self.base = BaseRobot(self.conn, self.error_handler)
        self.servo = ServoController(self.conn, self.error_handler)
        self.motion = MotionController(self.conn, self.servo, self.error_handler)
        self.status = StatusMonitor(self.conn, self.error_handler)
        
        # 尝试连接机器人
        self.conn.connect()
    
    # ========== 向后兼容的委托方法 ==========
    
    # 连接相关方法
    def is_robot_connected(self):
        """检查机器人是否已连接"""
        return self.base.is_robot_connected()
    
    def disconnect(self):
        """断开与机器人的连接"""
        return self.base.disconnect()
    
    def get_connection_status(self):
        """获取连接状态"""
        return self.base.get_connection_status()
    
    def clear_error(self):
        """清除机器人错误状态"""
        return self.base.clear_error()
    
    # 位置相关方法
    def get_current_position(self, coord_type=1):
        """获取机器人末端当前位置"""
        return self.base.get_current_position(coord_type)
    
    def get_robot_type(self):
        """获取机器人类型"""
        return self.base.get_robot_type()
    
    # 伺服控制方法
    def enable_servos(self):
        """打开机器人伺服电机（增强版）"""
        return self.servo.enable_servos()
    
    def disable_servos(self):
        """关闭机器人伺服电机（增强版）"""
        return self.servo.disable_servos()
    
    def get_servo_state(self):
        """获取伺服电机状态"""
        return self.servo.get_servo_state()
    
    def check_servo_ready_for_motion(self):
        """检查伺服状态是否适合执行运动操作"""
        return self.servo.check_servo_ready_for_motion()
    
    def set_servo_poweron(self):
        """机器人上电"""
        return self.servo.set_servo_poweron()
    
    def set_servo_poweroff(self):
        """机器人下电"""
        return self.servo.set_servo_poweroff()
    
    # 运动控制方法
    def move_linear(self, target_pos, velocity, blending_radius):
        """发送一个阻塞式的线性运动指令 (MoveL)"""
        return self.motion.move_linear(target_pos, velocity, blending_radius)
    
    def robot_movej(self, target_pos, velocity, blending_radius):
        """发送一个阻塞式的关节运动指令 (MoveJ)"""
        return self.motion.move_joint(target_pos, velocity, blending_radius)
    
    def set_speed(self, speed_percentage):
        """设置机器人全局速度比例"""
        return self.motion.set_speed(speed_percentage)
    
    def get_speed(self):
        """获取机器人当前速度比例"""
        return self.motion.get_speed()
    
    # 状态监控方法
    def get_robot_running_state(self):
        """获取机器人运行状态"""
        return self.status.get_robot_running_state()
    
    def get_detailed_status(self):
        """获取详细的机器人状态信息"""
        return self.status.get_detailed_status()
    
    def monitor_status_continuously(self, duration=10, interval=1):
        """连续监控机器人状态"""
        return self.status.monitor_status_continuously(duration, interval)
    
    def check_system_health(self):
        """检查系统健康状态"""
        return self.status.check_system_health()
    
    def print_status_summary(self):
        """打印状态摘要"""
        return self.status.print_status_summary()
    
    # ========== 新增的便捷方法 ==========
    
    def quick_start(self):
        """
        快速启动机器人（一键操作）
        
        执行标准的启动流程：清除错误 -> 使能伺服 -> 检查状态
        
        Returns:
            bool: 启动成功返回True，失败返回False
        """
        print("开始快速启动机器人...")
        
        # 1. 清除错误
        if not self.clear_error():
            print("⚠️ 清除错误失败，但继续启动流程")
        
        # 2. 使能伺服
        if not self.enable_servos():
            print("✗ 快速启动失败：伺服使能失败")
            return False
        
        # 3. 检查状态
        if not self.check_servo_ready_for_motion():
            print("✗ 快速启动失败：伺服状态不适合运动")
            return False
        
        print("✓ 机器人快速启动成功，可以执行运动指令")
        return True
    
    def safe_shutdown(self):
        """
        安全关闭机器人（一键操作）
        
        执行标准的关闭流程：下电 -> 关闭伺服 -> 断开连接
        
        Returns:
            bool: 关闭成功返回True，失败返回False
        """
        print("开始安全关闭机器人...")
        
        success = True
        
        # 1. 下电（如果处于运行状态）
        servo_state = self.get_servo_state()
        if servo_state and servo_state['status_code'] == 3:
            if not self.set_servo_poweroff():
                print("⚠️ 下电失败，但继续关闭流程")
                success = False
        
        # 2. 关闭伺服
        if not self.disable_servos():
            print("⚠️ 关闭伺服失败，但继续关闭流程")
            success = False
        
        # 3. 断开连接
        self.disconnect()
        
        if success:
            print("✓ 机器人安全关闭成功")
        else:
            print("⚠️ 机器人关闭完成，但过程中有警告")
        
        return success
    
    def is_development_mode(self):
        """
        检查是否处于开发模式
        
        Returns:
            bool: True表示开发模式，False表示正常模式
        """
        return self.conn.is_development_mode()
    
    def get_module_info(self):
        """
        获取模块信息
        
        Returns:
            dict: 包含各模块信息的字典
        """
        return {
            "connection": {
                "class": self.conn.__class__.__name__,
                "connected": self.conn.is_robot_connected(),
                "development_mode": self.conn.is_development_mode()
            },
            "modules": {
                "base": self.base.__class__.__name__,
                "servo": self.servo.__class__.__name__,
                "motion": self.motion.__class__.__name__,
                "status": self.status.__class__.__name__,
                "error_handler": self.error_handler.__class__.__name__
            }
        }
    
    def __del__(self):
        """
        析构函数，确保资源被正确释放
        """
        try:
            self.disconnect()
        except:
            pass  # 忽略析构时的异常
