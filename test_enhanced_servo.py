#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强伺服控制测试脚本

测试增强后的enable_servos()方法，验证其是否按照INEXBOT官方文档
正确处理各种伺服状态和错误情况。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import time
from hardware.robot_interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT


class EnhancedServoTester:
    """增强伺服控制测试类"""
    
    def __init__(self):
        self.robot = None
        self.test_results = {}
    
    def print_test_header(self, title):
        """打印测试标题"""
        print("\n" + "="*60)
        print(f"  {title}")
        print("="*60)
    
    def print_section_header(self, title):
        """打印章节标题"""
        print(f"\n--- {title} ---")
    
    def connect_robot(self):
        """连接机器人"""
        self.print_test_header("连接机器人")
        
        try:
            print(f"正在连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
            self.robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
            
            if self.robot.is_robot_connected():
                print("✓ 机器人连接成功")
                return True
            else:
                print("✗ 机器人连接失败")
                return False
                
        except Exception as e:
            print(f"✗ 连接异常: {e}")
            return False
    
    def test_servo_state_detection(self):
        """测试伺服状态检测功能"""
        self.print_test_header("测试1: 伺服状态检测功能")
        
        try:
            # 测试获取伺服状态
            self.print_section_header("获取当前伺服状态")
            servo_state = self.robot.get_servo_state()
            
            if servo_state:
                print(f"✓ 伺服状态获取成功:")
                print(f"  - 状态码: {servo_state['status_code']}")
                print(f"  - 描述: {servo_state['description']}")
                print(f"  - 已使能: {servo_state['enabled']}")
                self.test_results["servo_state_detection"] = True
                return True
            else:
                print("✗ 无法获取伺服状态")
                self.test_results["servo_state_detection"] = False
                return False
                
        except Exception as e:
            print(f"✗ 伺服状态检测测试失败: {e}")
            self.test_results["servo_state_detection"] = False
            return False
    
    def test_motion_readiness_check(self):
        """测试运动准备状态检查"""
        self.print_test_header("测试2: 运动准备状态检查")
        
        try:
            self.print_section_header("检查当前是否适合执行运动")
            is_ready = self.robot.check_servo_ready_for_motion()
            
            print(f"运动准备状态: {'✓ 适合' if is_ready else '✗ 不适合'}")
            self.test_results["motion_readiness"] = True
            return True
            
        except Exception as e:
            print(f"✗ 运动准备状态检查失败: {e}")
            self.test_results["motion_readiness"] = False
            return False
    
    def test_enhanced_servo_enable(self):
        """测试增强的伺服使能功能"""
        self.print_test_header("测试3: 增强的伺服使能功能")
        
        try:
            # 首先确保伺服处于停止状态
            self.print_section_header("准备测试环境 - 关闭伺服")
            self.robot.disable_servos()
            time.sleep(2)
            
            # 测试增强的使能功能
            self.print_section_header("执行增强的伺服使能")
            enable_result = self.robot.enable_servos()
            
            if enable_result:
                print("✓ 增强伺服使能成功")
                
                # 验证使能结果
                time.sleep(1)
                servo_state = self.robot.get_servo_state()
                if servo_state and servo_state['enabled']:
                    print("✓ 伺服使能状态验证成功")
                    self.test_results["enhanced_servo_enable"] = True
                    return True
                else:
                    print("✗ 伺服使能状态验证失败")
                    self.test_results["enhanced_servo_enable"] = False
                    return False
            else:
                print("✗ 增强伺服使能失败")
                self.test_results["enhanced_servo_enable"] = False
                return False
                
        except Exception as e:
            print(f"✗ 增强伺服使能测试失败: {e}")
            self.test_results["enhanced_servo_enable"] = False
            return False
    
    def test_enhanced_servo_disable(self):
        """测试增强的伺服关闭功能"""
        self.print_test_header("测试4: 增强的伺服关闭功能")
        
        try:
            # 确保伺服处于使能状态
            self.print_section_header("准备测试环境 - 确保伺服使能")
            if not self.robot.enable_servos():
                print("⚠️ 无法使能伺服，跳过关闭测试")
                return False
            
            time.sleep(1)
            
            # 测试增强的关闭功能
            self.print_section_header("执行增强的伺服关闭")
            disable_result = self.robot.disable_servos()
            
            if disable_result:
                print("✓ 增强伺服关闭成功")
                
                # 验证关闭结果
                time.sleep(1)
                servo_state = self.robot.get_servo_state()
                if servo_state and not servo_state['enabled']:
                    print("✓ 伺服关闭状态验证成功")
                    self.test_results["enhanced_servo_disable"] = True
                    return True
                else:
                    print("⚠️ 伺服关闭状态验证异常，但功能可能正常")
                    self.test_results["enhanced_servo_disable"] = True
                    return True
            else:
                print("✗ 增强伺服关闭失败")
                self.test_results["enhanced_servo_disable"] = False
                return False
                
        except Exception as e:
            print(f"✗ 增强伺服关闭测试失败: {e}")
            self.test_results["enhanced_servo_disable"] = False
            return False
    
    def test_error_handling(self):
        """测试错误处理功能"""
        self.print_test_header("测试5: 错误处理功能")
        
        try:
            self.print_section_header("测试错误清除功能")
            clear_result = self.robot.clear_error()
            
            print(f"错误清除结果: {'✓ 成功' if clear_result else '⚠️ 失败或无错误'}")
            self.test_results["error_handling"] = True
            return True
            
        except Exception as e:
            print(f"✗ 错误处理测试失败: {e}")
            self.test_results["error_handling"] = False
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("开始增强伺服控制功能测试")
        print(f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 连接机器人
        if not self.connect_robot():
            print("\n✗ 无法连接机器人，测试终止")
            return False
        
        # 运行测试序列
        tests = [
            ("伺服状态检测", self.test_servo_state_detection),
            ("运动准备状态检查", self.test_motion_readiness_check),
            ("增强伺服使能", self.test_enhanced_servo_enable),
            ("增强伺服关闭", self.test_enhanced_servo_disable),
            ("错误处理", self.test_error_handling),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed_tests += 1
                time.sleep(1)  # 测试间隔
            except Exception as e:
                print(f"✗ 测试 '{test_name}' 执行异常: {e}")
        
        # 打印测试总结
        self.print_test_summary(passed_tests, total_tests)
        
        # 清理资源
        self.cleanup()
        
        return passed_tests == total_tests
    
    def print_test_summary(self, passed, total):
        """打印测试总结"""
        self.print_test_header("测试总结")
        
        print(f"总测试数: {total}")
        print(f"通过测试: {passed}")
        print(f"失败测试: {total - passed}")
        print(f"成功率: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！增强伺服控制功能工作正常。")
        else:
            print(f"\n⚠️ {total - passed} 个测试失败，请检查相关功能。")
        
        # 详细结果
        print("\n详细测试结果:")
        for test_name, result in self.test_results.items():
            status = "✓ 通过" if result else "✗ 失败"
            print(f"  - {test_name}: {status}")
    
    def cleanup(self):
        """清理资源"""
        if self.robot:
            try:
                print("\n正在清理资源...")
                self.robot.disable_servos()
                self.robot.disconnect()
                print("✓ 资源清理完成")
            except Exception as e:
                print(f"⚠️ 资源清理异常: {e}")


def main():
    """主函数"""
    tester = EnhancedServoTester()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️ 用户中断测试")
        tester.cleanup()
        sys.exit(1)
    except Exception as e:
        print(f"\n✗ 测试程序异常: {e}")
        tester.cleanup()
        sys.exit(1)


if __name__ == "__main__":
    main()
