# src/robot/base.py
"""
基础机器人接口模块

该模块提供机器人的基础功能，包括位置获取、连接状态查询等。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
# 添加lib目录到Python路径
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
except ImportError:
    nrc = None

from ..core.validators import ParameterValidator


class BaseRobot:
    """
    基础机器人接口
    
    提供机器人的基础功能，如位置获取、连接状态查询等。
    """
    
    def __init__(self, connection_manager, error_handler):
        """
        初始化基础机器人接口
        
        Args:
            connection_manager: 连接管理器实例
            error_handler: 错误处理器实例
        """
        self.conn = connection_manager
        self.error_handler = error_handler
    
    def get_current_position(self, coord_type=1):
        """
        获取机器人末端当前位置
        
        Args:
            coord_type (int): 坐标系类型
                0: 关节坐标系
                1: 直角坐标系 (默认)
                2: 工具坐标系
                3: 用户坐标系
        
        Returns:
            list: 包含位置数据的列表，如果获取失败返回None
            - 直角坐标系: [X, Y, Z, A, B, C] (mm, deg)
            - 关节坐标系: [J1, J2, J3, J4, J5, J6] (deg)
        """
        # 参数验证
        try:
            ParameterValidator.validate_coordinate_type(coord_type)
        except ValueError as e:
            print(f"✗ 参数错误: {e}")
            return None
        
        if self.conn.is_development_mode():
            print(f"💡 开发模式：模拟获取当前位置 (坐标系类型: {coord_type})")
            return [100.0, 200.0, 300.0, 0.0, 0.0, 0.0]  # 模拟位置数据
        
        if not self.conn.is_robot_connected():
            print("✗ 机器人未连接，无法获取位置")
            return None
        
        if nrc is None:
            print("⚠️ nrc_interface 不可用")
            return None
        
        try:
            # 创建VectorDouble对象来接收位置数据
            pos_data = nrc.VectorDouble()
            
            # 调用API获取当前位置
            result = nrc.get_current_position(self.conn.get_socket_fd(), coord_type, pos_data)
            
            if result == 0:  # SUCCESS
                # 将VectorDouble转换为Python列表
                position = []
                for i in range(6):  # X, Y, Z, A, B, C 或 J1-J6
                    if i < len(pos_data):
                        position.append(pos_data[i])
                    else:
                        position.append(0.0)
                
                coord_names = {
                    0: "关节坐标系",
                    1: "直角坐标系", 
                    2: "工具坐标系",
                    3: "用户坐标系"
                }
                print(f"✓ 获取当前位置成功 ({coord_names.get(coord_type, '未知坐标系')}): {position}")
                return position
            else:
                print(f"✗ 获取当前位置失败，错误代码: {result}")
                return None
                
        except Exception as e:
            print(f"✗ 获取当前位置异常: {e}")
            return None
    
    def get_connection_status(self):
        """
        检查当前连接状态
        
        Returns:
            int: 连接状态值，具体含义参考INEXBOT SDK文档
        """
        return self.conn.get_connection_status()
    
    def is_robot_connected(self):
        """
        检查机器人是否已连接
        
        Returns:
            bool: True表示已连接，False表示未连接
        """
        return self.conn.is_robot_connected()
    
    def get_socket_fd(self):
        """
        获取套接字文件描述符
        
        Returns:
            int: 套接字文件描述符，用于后续的机器人控制操作
        """
        return self.conn.get_socket_fd()
    
    def disconnect(self):
        """
        断开与机器人的连接
        """
        self.conn.disconnect()
    
    def clear_error(self):
        """
        清除机器人错误状态
        
        Returns:
            bool: 清除成功返回True，失败返回False
        """
        return self.error_handler.clear_error()
    
    def get_robot_type(self):
        """
        获取机器人类型
        
        Returns:
            int: 机器人类型代码，失败返回None
        """
        if self.conn.is_development_mode():
            print("💡 开发模式：模拟机器人类型")
            return 1  # 模拟机器人类型
        
        if not self.conn.is_robot_connected():
            print("✗ 机器人未连接，无法获取机器人类型")
            return None
        
        if nrc is None:
            print("⚠️ nrc_interface 不可用")
            return None
        
        try:
            robot_type = nrc.VectorInt()
            result = nrc.get_robot_type(self.conn.get_socket_fd(), robot_type)
            
            if result == 0 and len(robot_type) > 0:
                print(f"✓ 机器人类型: {robot_type[0]}")
                return robot_type[0]
            else:
                print(f"✗ 获取机器人类型失败，错误代码: {result}")
                return None
                
        except Exception as e:
            print(f"✗ 获取机器人类型异常: {e}")
            return None
