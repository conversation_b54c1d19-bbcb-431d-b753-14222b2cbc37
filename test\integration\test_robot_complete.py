# test_robot_enhanced.py
"""
机器人增强功能测试脚本

该脚本实现系统性测试方案的阶段1和阶段2：
- 阶段1：基础连接测试
- 阶段2：伺服和状态测试

测试内容包括：
- 连接管理和状态查询
- 伺服控制和状态监控
- 错误处理和恢复
- 速度控制功能
- 关节运动功能

重要提示：运行前请确保机器人处于安全的工作环境。
"""

import time
import sys
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

from src.robot.interface import RobotInterface
from config import ROBOT_IP, ROBOT_PORT


class RobotEnhancedTester:
    """
    机器人增强功能测试类
    """
    
    def __init__(self):
        self.robot = None
        self.test_results = {
            "stage1_connection": False,
            "stage2_servo_status": False,
            "error_handling": False,
            "speed_control": False,
            "joint_motion": False
        }
    
    def print_stage_header(self, stage_name):
        """打印测试阶段标题"""
        print("\n" + "=" * 60)
        print(f"测试阶段: {stage_name}")
        print("=" * 60)
    
    def print_test_header(self, test_name):
        """打印测试项目标题"""
        print(f"\n--- {test_name} ---")
    
    def stage1_connection_tests(self):
        """
        阶段1：基础连接测试
        测试顺序: 连接 → 状态查询 → 断开
        """
        self.print_stage_header("阶段1 - 基础连接测试")
        
        try:
            # 测试1.1：机器人连接
            self.print_test_header("测试1.1 - 机器人连接")
            print(f"正在连接机器人 {ROBOT_IP}:{ROBOT_PORT}...")
            
            self.robot = RobotInterface(ROBOT_IP, ROBOT_PORT)
            print("✓ 机器人连接成功")
            
            # 测试1.2：连接状态查询
            self.print_test_header("测试1.2 - 连接状态查询")
            
            # 检查连接状态
            if self.robot.is_robot_connected():
                print("✓ 机器人连接状态检查通过")
            else:
                print("✗ 机器人连接状态检查失败")
                return False
            
            # 获取连接状态码
            status_code = self.robot.get_connection_status()
            print(f"✓ 连接状态码: {status_code}")
            
            # 获取套接字描述符
            socket_fd = self.robot.get_socket_fd()
            print(f"✓ 套接字描述符: {socket_fd}")
            
            # 测试1.3：运行状态查询
            self.print_test_header("测试1.3 - 运行状态查询")
            
            running_state = self.robot.get_robot_running_state()
            if running_state:
                print(f"✓ 机器人运行状态: {running_state['description']} (代码: {running_state['status']})")
            else:
                print("✗ 获取运行状态失败")
                return False
            
            self.test_results["stage1_connection"] = True
            print("\n✓ 阶段1测试完成 - 基础连接功能正常")
            return True
            
        except Exception as e:
            print(f"✗ 阶段1测试失败: {e}")
            return False
    
    def stage2_servo_status_tests(self):
        """
        阶段2：伺服和状态测试
        测试顺序: 伺服使能 → 状态查询 → 错误处理 → 伺服关闭
        """
        self.print_stage_header("阶段2 - 伺服和状态测试")
        
        if not self.robot:
            print("✗ 机器人未连接，跳过阶段2测试")
            return False
        
        try:
            # 测试2.1：伺服状态查询（使能前）
            self.print_test_header("测试2.1 - 伺服状态查询（使能前）")
            
            servo_state = self.robot.get_servo_state()
            if servo_state:
                print(f"✓ 伺服状态: {'已使能' if servo_state['enabled'] else '未使能'} (代码: {servo_state['status_code']})")
            else:
                print("✗ 获取伺服状态失败")
            
            # 测试2.2：清除错误状态
            self.print_test_header("测试2.2 - 清除错误状态")

            print("清除可能的错误状态...")
            if self.robot.clear_error():
                print("✓ 错误状态清除成功")
            else:
                print("⚠️ 清除错误失败或无错误需要清除")

            time.sleep(1)

            # 测试2.3：伺服使能
            self.print_test_header("测试2.3 - 伺服使能")

            print("正在设置伺服为就绪状态...")
            if not self.robot.enable_servos():
                print("✗ 伺服使能失败")
                return False

            time.sleep(2)  # 等待伺服使能完成
            
            # 验证伺服状态
            servo_state = self.robot.get_servo_state()
            if servo_state and servo_state['enabled']:
                print("✓ 伺服使能成功")
            else:
                print("✗ 伺服使能验证失败")
                return False
            
            # 测试2.4：尝试上电
            self.print_test_header("测试2.4 - 尝试上电")

            servo_state = self.robot.get_servo_state()
            if servo_state and servo_state['status_code'] == 1:
                print("伺服处于就绪状态，尝试上电...")
                if self.robot.set_servo_poweron():
                    print("✓ 机器人上电成功")
                    time.sleep(2)
                else:
                    print("⚠️ 机器人上电失败")
            else:
                print("⚠️ 伺服状态不适合上电")

            # 测试2.5：运行状态监控
            self.print_test_header("测试2.5 - 运行状态监控")

            for i in range(3):
                running_state = self.robot.get_robot_running_state()
                if running_state:
                    print(f"  监控{i+1}: {running_state['description']}")
                time.sleep(1)

            print("✓ 运行状态监控正常")

            # 测试2.6：错误处理验证
            self.print_test_header("测试2.6 - 错误处理验证")

            print("验证错误清除功能...")
            if self.robot.clear_error():
                print("✓ 错误清除功能正常")
                self.test_results["error_handling"] = True
            else:
                print("✓ 错误清除功能正常（无错误需要清除）")
                self.test_results["error_handling"] = True

            # 测试2.7：伺服关闭
            self.print_test_header("测试2.7 - 伺服关闭")
            
            print("正在关闭伺服电机...")
            self.robot.disable_servos()
            time.sleep(1)
            
            # 验证伺服状态
            servo_state = self.robot.get_servo_state()
            if servo_state and not servo_state['enabled']:
                print("✓ 伺服关闭成功")
            else:
                print("✗ 伺服关闭验证失败")
                return False
            
            self.test_results["stage2_servo_status"] = True
            print("\n✓ 阶段2测试完成 - 伺服和状态功能正常")
            return True
            
        except Exception as e:
            print(f"✗ 阶段2测试失败: {e}")
            return False
    
    def speed_control_tests(self):
        """
        速度控制功能测试
        """
        self.print_test_header("速度控制功能测试")
        
        if not self.robot:
            print("✗ 机器人未连接，跳过速度控制测试")
            return False
        
        try:
            # 获取当前速度
            current_speed = self.robot.get_speed()
            if current_speed is not None:
                print(f"✓ 当前速度比例: {current_speed}%")
            
            # 测试速度设置
            test_speeds = [50.0, 75.0, 100.0]
            
            for speed in test_speeds:
                print(f"设置速度为 {speed}%...")
                if self.robot.set_speed(speed):
                    time.sleep(0.5)
                    # 验证速度设置
                    actual_speed = self.robot.get_speed()
                    if actual_speed is not None and abs(actual_speed - speed) < 1.0:
                        print(f"✓ 速度设置成功: {actual_speed}%")
                    else:
                        print(f"✗ 速度设置验证失败: 期望{speed}%, 实际{actual_speed}%")
                        return False
                else:
                    print(f"✗ 设置速度{speed}%失败")
                    return False
            
            # 恢复原始速度
            if current_speed is not None:
                self.robot.set_speed(current_speed)
                print(f"✓ 恢复原始速度: {current_speed}%")
            
            self.test_results["speed_control"] = True
            print("✓ 速度控制功能测试通过")
            return True
            
        except Exception as e:
            print(f"✗ 速度控制测试失败: {e}")
            return False
    
    def run_all_tests(self):
        """
        运行所有测试
        """
        print("机器人增强功能测试开始")
        print("测试范围：连接管理、状态查询、伺服控制、错误处理、速度控制")
        
        try:
            # 阶段1：基础连接测试
            if not self.stage1_connection_tests():
                print("\n✗ 阶段1测试失败，终止后续测试")
                return False
            
            # 阶段2：伺服和状态测试
            if not self.stage2_servo_status_tests():
                print("\n✗ 阶段2测试失败，终止后续测试")
                return False
            
            # 速度控制测试
            self.speed_control_tests()
            
            # 测试结果汇总
            self.print_test_summary()
            
            return True
            
        except Exception as e:
            print(f"\n✗ 测试过程中发生未预期错误: {e}")
            return False
        
        finally:
            # 确保安全断开连接
            if self.robot:
                print("\n正在安全断开连接...")
                self.robot.disconnect()
    
    def print_test_summary(self):
        """
        打印测试结果汇总
        """
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✓ 通过" if result else "✗ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总体结果: {passed_tests}/{total_tests} 项测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！机器人增强功能正常")
        else:
            print("⚠️  部分测试失败，请检查相关功能")


def main():
    """
    主函数
    """
    print("警告：运行此测试前请确保：")
    print("1. 机器人处于安全的工作环境")
    print("2. 周围没有障碍物")
    print("3. 有紧急停止按钮可用")
    print("4. 机器人控制器正常运行")
    print()
    
    # 等待用户确认
    user_input = input("确认安全条件后，输入 'yes' 开始测试: ")
    if user_input.lower() != 'yes':
        print("测试已取消")
        return
    
    # 创建测试器并运行测试
    tester = RobotEnhancedTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 增强功能测试完成")
    else:
        print("\n❌ 测试过程中出现问题")
        sys.exit(1)


if __name__ == "__main__":
    main()
