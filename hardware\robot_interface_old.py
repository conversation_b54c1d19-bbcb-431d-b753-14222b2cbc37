# hardware/robot_interface.py
"""
INEXBOT机械臂硬件接口模块

该模块封装了与INEXBOT机械臂的所有通信细节，
提供一个干净的硬件抽象层。
"""

import nrc_interface as nrc


class RobotInterface:
    """
    INEXBOT机械臂接口类
    
    该类封装了与机械臂的连接、断开和状态检查功能，
    为上层应用提供统一的机器人控制接口。
    """
    
    def __init__(self, ip, port):
        """
        初始化机器人接口
        
        Args:
            ip (str): 机器人的IP地址
            port (int): 机器人的通信端口
            
        Raises:
            ConnectionError: 当连接失败时抛出异常
        """
        self.ip = ip
        self.port = port
        self.socket_fd = None
        self.is_connected = False
        
        # 尝试连接机器人
        print(f"正在连接机器人 {ip}:{port}...")
        result = nrc.connect_robot(ip, str(port))
        
        # 检查连接结果
        if result < 0:
            error_msg = f"连接机器人失败！错误代码: {result}"
            print(error_msg)
            raise ConnectionError(error_msg)
        
        # 连接成功，保存套接字文件描述符
        self.socket_fd = result
        self.is_connected = True
        print(f"机器人连接成功！套接字FD: {self.socket_fd}")
    
    def disconnect(self):
        """
        安全地断开与机器人的连接
        """
        if self.socket_fd is not None and self.is_connected:
            print("正在断开机器人连接...")
            nrc.disconnect_robot(self.socket_fd)
            self.is_connected = False
            print("机器人连接已断开")
        else:
            print("机器人未连接，无需断开")
    
    def get_connection_status(self):
        """
        检查当前连接状态
        
        Returns:
            int: 连接状态值，具体含义参考INEXBOT SDK文档
        """
        if self.socket_fd is not None:
            status = nrc.get_connection_status(self.socket_fd)
            return status
        else:
            return -1  # 未连接状态
    
    def is_robot_connected(self):
        """
        检查机器人是否已连接
        
        Returns:
            bool: True表示已连接，False表示未连接
        """
        return self.is_connected and self.socket_fd is not None
    
    def get_socket_fd(self):
        """
        获取套接字文件描述符
        
        Returns:
            int: 套接字文件描述符，用于后续的机器人控制操作
        """
        return self.socket_fd

    def check_servo_ready_for_motion(self):
        """
        检查伺服状态是否适合执行运动操作

        根据INEXBOT官方文档，只有在伺服状态为就绪(1)或运行(3)时才能执行运动操作。

        Returns:
            bool: 适合运动返回True，不适合返回False
        """
        servo_state = self.get_servo_state()
        if servo_state is None:
            print("✗ 无法获取伺服状态，不建议执行运动")
            return False

        status_code = servo_state['status_code']
        description = servo_state['description']

        if status_code in [1, 3]:  # 就绪状态或运行状态
            print(f"✓ 伺服状态适合运动: {description}")
            return True
        elif status_code == 0:  # 停止状态
            print(f"⚠️ 伺服处于停止状态，需要先使能: {description}")
            return False
        elif status_code == 2:  # 报警状态
            print(f"✗ 伺服处于报警状态，需要先清除错误: {description}")
            return False
        else:
            print(f"⚠️ 伺服状态未知，不建议执行运动: {description}")
            return False
    
    def enable_servos(self):
        """
        打开机器人伺服电机

        根据INEXBOT官方文档增强的伺服使能方法：
        1. 检查当前伺服状态
        2. 清除任何伺服错误（如果存在）
        3. 只在安全状态下（停止状态0或就绪状态1）尝试设置伺服状态
        4. 处理报警状态（状态2）和运行状态（状态3）的特殊情况
        5. 提供详细的错误处理和状态反馈

        Returns:
            bool: 使能成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法使能伺服")
            return False

        print("正在执行增强的伺服使能流程...")

        # 步骤1：获取当前伺服状态
        print("步骤1: 检查当前伺服状态")
        current_servo_state = self.get_servo_state()
        if current_servo_state is None:
            print("✗ 无法获取当前伺服状态，使能失败")
            return False

        status_code = current_servo_state['status_code']
        description = current_servo_state['description']
        print(f"当前伺服状态: {description} (代码: {status_code})")

        # 如果已经是就绪状态或运行状态，无需重复使能
        if status_code == 1:
            print("✓ 伺服已处于就绪状态，无需重复使能")
            return True
        elif status_code == 3:
            print("✓ 伺服已处于运行状态，已经使能")
            return True

        # 步骤2：处理报警状态（状态2）
        if status_code == 2:
            print("⚠️ 检测到伺服处于报警状态，尝试清除错误...")
            if not self.clear_error():
                print("✗ 清除伺服错误失败，无法继续使能")
                return False

            # 等待错误清除生效
            import time
            time.sleep(1)

            # 重新检查状态
            updated_servo_state = self.get_servo_state()
            if updated_servo_state is None:
                print("✗ 清除错误后无法获取伺服状态")
                return False

            status_code = updated_servo_state['status_code']
            print(f"清除错误后伺服状态: {updated_servo_state['description']} (代码: {status_code})")

            # 如果仍然是报警状态，无法继续
            if status_code == 2:
                print("✗ 清除错误后伺服仍处于报警状态，请检查硬件或手动排除故障")
                return False

        # 步骤3：处理运行状态（状态3）- 理论上不应该出现，但提供处理逻辑
        elif status_code == 3:
            print("⚠️ 伺服已处于运行状态，建议先下电再重新使能")
            return True  # 运行状态也算是使能状态

        # 步骤4：预防性清除错误（确保系统无错误）
        print("步骤2: 预防性清除可能的错误状态")
        if not self.clear_error():
            print("⚠️ 预防性错误清除失败，但继续尝试使能")

        # 等待错误清除生效
        import time
        time.sleep(0.5)

        # 步骤5：尝试设置伺服为就绪状态
        print("步骤3: 设置伺服为就绪状态")
        if status_code not in [0, 1]:
            print(f"✗ 伺服状态 {status_code} 不允许直接设置，请先处理当前状态")
            return False

        result = nrc.set_servo_state(self.socket_fd, 1)
        if result != 0:
            print(f"✗ 伺服使能失败，错误代码: {result}")
            return False

        # 步骤6：验证使能结果
        print("步骤4: 验证伺服使能结果")
        time.sleep(1)  # 等待状态更新

        final_servo_state = self.get_servo_state()
        if final_servo_state is None:
            print("⚠️ 无法验证伺服使能结果")
            return False

        final_status_code = final_servo_state['status_code']
        final_description = final_servo_state['description']

        if final_status_code == 1:
            print(f"✓ 伺服使能成功: {final_description}")
            return True
        else:
            print(f"✗ 伺服使能验证失败: {final_description} (代码: {final_status_code})")
            return False

    def disable_servos(self):
        """
        关闭机器人伺服电机

        增强的伺服关闭方法：
        1. 检查当前伺服状态
        2. 如果处于运行状态，先下电再关闭伺服
        3. 验证关闭结果
        4. 提供详细的状态反馈

        Returns:
            bool: 关闭成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法关闭伺服")
            return False

        print("正在执行增强的伺服关闭流程...")

        # 步骤1：获取当前伺服状态
        print("步骤1: 检查当前伺服状态")
        current_servo_state = self.get_servo_state()
        if current_servo_state is None:
            print("⚠️ 无法获取当前伺服状态，尝试直接关闭")
        else:
            status_code = current_servo_state['status_code']
            description = current_servo_state['description']
            print(f"当前伺服状态: {description} (代码: {status_code})")

            # 如果已经是停止状态，无需重复关闭
            if status_code == 0:
                print("✓ 伺服已处于停止状态，无需重复关闭")
                return True

            # 如果处于运行状态，建议先下电
            if status_code == 3:
                print("步骤2: 检测到运行状态，先执行下电操作")
                if self.set_servo_poweroff():
                    print("✓ 下电成功，继续关闭伺服")
                    # 等待下电完成
                    import time
                    time.sleep(1)
                else:
                    print("⚠️ 下电失败，但继续尝试关闭伺服")

        # 步骤3：设置伺服为停止状态
        print("步骤3: 设置伺服为停止状态")
        result = nrc.set_servo_state(self.socket_fd, 0)
        if result != 0:
            print(f"✗ 伺服关闭失败，错误代码: {result}")
            return False

        # 步骤4：验证关闭结果
        print("步骤4: 验证伺服关闭结果")
        import time
        time.sleep(1)  # 等待状态更新

        final_servo_state = self.get_servo_state()
        if final_servo_state is None:
            print("⚠️ 无法验证伺服关闭结果")
            return False

        final_status_code = final_servo_state['status_code']
        final_description = final_servo_state['description']

        if final_status_code == 0:
            print(f"✓ 伺服关闭成功: {final_description}")
            return True
        else:
            print(f"⚠️ 伺服关闭验证异常: {final_description} (代码: {final_status_code})")
            # 即使状态不是0，只要不是错误状态，也可能是正常的
            return final_status_code != 2  # 只要不是报警状态就认为成功

    def get_current_position(self, coord_type=1):
        """
        获取机器人末端当前位置

        Args:
            coord_type (int): 坐标系类型
                0: 关节坐标系
                1: 直角坐标系 (默认)
                2: 工具坐标系
                3: 用户坐标系

        Returns:
            list: 包含位置数据的列表，如果获取失败返回None
            - 直角坐标系: [X, Y, Z, A, B, C] (mm, deg)
            - 关节坐标系: [J1, J2, J3, J4, J5, J6] (deg)
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法获取位置")
            return None

        # 创建VectorDouble对象来接收位置数据
        pos_data = nrc.VectorDouble()

        # 调用API获取当前位置，根据官方文档需要传入坐标系类型
        result = nrc.get_current_position(self.socket_fd, coord_type, pos_data)

        if result == 0:  # SUCCESS
            # 将VectorDouble转换为Python列表
            position = []
            for i in range(6):  # X, Y, Z, A, B, C
                if i < len(pos_data):
                    position.append(pos_data[i])
                else:
                    position.append(0.0)
            return position
        else:
            print(f"获取当前位置失败，错误代码: {result}")
            return None

    def move_linear(self, target_pos, velocity, blending_radius):
        """
        发送一个阻塞式的线性运动指令 (MoveL)

        增强的线性运动方法，包含伺服状态检查和安全验证。
        程序会在此等待直到运动完成。

        Args:
            target_pos (list): 包含6个浮点数（X,Y,Z,A,B,C）的Python列表
            velocity (float): 运动速度 (单位: mm/s)
            blending_radius (float): 路径平滑过渡的半径/等级 (pl 参数)

        Returns:
            bool: 运动成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法执行运动")
            return False

        if len(target_pos) != 6:
            print("错误：目标位置必须包含6个坐标值 (X,Y,Z,A,B,C)")
            return False

        # 安全检查：验证伺服状态
        if not self.check_servo_ready_for_motion():
            print("✗ 伺服状态不适合执行运动，请先检查并使能伺服")
            return False

        print(f"正在执行线性移动至: {target_pos}")

        # 创建MoveCmd对象
        move_cmd = nrc.MoveCmd()

        # 创建VectorDouble对象并填充目标位置
        target_vector = nrc.VectorDouble()
        for pos in target_pos:
            target_vector.push_back(float(pos))

        # 填充MoveCmd对象的属性
        move_cmd.targetPosValue = target_vector
        move_cmd.velocity = float(velocity)
        move_cmd.pl = float(blending_radius)
        move_cmd.toolNum = 1  # 默认工具
        move_cmd.userNum = 1  # 默认坐标系

        # 执行线性运动
        result = nrc.robot_movel(self.socket_fd, move_cmd)

        if result == 0:  # SUCCESS
            print("线性运动执行成功")
            return True
        else:
            print(f"线性运动执行失败，错误代码: {result}")
            return False

    def get_robot_running_state(self):
        """
        获取机器人运行状态

        这是3D打印应用中的关键功能，用于实时监控机器人状态。

        Returns:
            dict: 包含运行状态信息的字典，失败返回None
            格式: {"status": int, "description": str}
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法获取运行状态")
            return None

        # 创建VectorInt对象来接收状态数据
        status_data = nrc.VectorInt()

        # 调用API获取运行状态
        result = nrc.get_robot_running_state(self.socket_fd, status_data)

        if result == 0:  # SUCCESS
            if len(status_data) > 0:
                status_code = status_data[0]

                # 状态码映射（根据INEXBOT文档）
                status_descriptions = {
                    0: "停止状态",
                    1: "运行状态",
                    2: "暂停状态",
                    3: "错误状态",
                    4: "急停状态",
                    5: "准备状态"
                }

                description = status_descriptions.get(status_code, f"未知状态({status_code})")

                return {
                    "status": status_code,
                    "description": description
                }
            else:
                print("获取运行状态失败：返回数据为空")
                return None
        else:
            print(f"获取运行状态失败，错误代码: {result}")
            return None

    def clear_error(self):
        """
        清除机器人错误状态

        这是3D打印应用中的重要安全功能，用于程序化错误恢复。

        Returns:
            bool: 清除成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法清除错误")
            return False

        print("正在清除机器人错误状态...")
        result = nrc.clear_error(self.socket_fd)

        if result == 0:  # SUCCESS
            print("机器人错误状态已清除")
            return True
        else:
            print(f"清除错误状态失败，错误代码: {result}")
            return False

    def set_speed(self, speed_percentage):
        """
        设置机器人全局速度比例

        用于3D打印过程中动态调整运动速度。

        Args:
            speed_percentage (int): 速度比例，范围1-100 (%)

        Returns:
            bool: 设置成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法设置速度")
            return False

        # 参数验证
        if not isinstance(speed_percentage, (int, float)):
            print("错误：速度比例必须是数字")
            return False

        # 根据官方文档，速度参数应为整数，范围1-100
        speed_int = int(round(speed_percentage))
        if not (1 <= speed_int <= 100):
            print("错误：速度比例必须在1-100%范围内")
            return False

        print(f"设置机器人速度比例为: {speed_int}%")
        result = nrc.set_speed(self.socket_fd, speed_int)

        if result == 0:  # SUCCESS
            print(f"速度比例设置成功: {speed_percentage}%")
            return True
        else:
            print(f"设置速度比例失败，错误代码: {result}")
            return False

    def get_speed(self):
        """
        获取机器人当前速度比例

        Returns:
            int: 当前速度比例(%)，失败返回None
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法获取速度")
            return None

        # 根据官方文档，速度参数为int类型，使用VectorInt
        speed_data = nrc.VectorInt()

        # 调用API获取速度
        result = nrc.get_speed(self.socket_fd, speed_data)

        if result == 0:  # SUCCESS
            if len(speed_data) > 0:
                current_speed = speed_data[0]
                print(f"当前速度比例: {current_speed}%")
                return current_speed
            else:
                print("获取速度失败：返回数据为空")
                return None
        else:
            print(f"获取速度失败，错误代码: {result}")
            return None

    def set_servo_poweron(self):
        """
        机器人上电

        根据官方文档：调用该函数之前需要先调用set_servo_state(1)将伺服设置为就绪状态，
        机器人上电成功后调用get_servo_state()为3伺服运行状态。

        Returns:
            bool: 上电成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法上电")
            return False

        print("正在执行机器人上电...")
        result = nrc.set_servo_poweron(self.socket_fd)

        if result == 0:  # SUCCESS
            print("机器人上电成功")
            return True
        else:
            print(f"机器人上电失败，错误代码: {result}")
            return False

    def set_servo_poweroff(self):
        """
        机器人下电

        根据官方文档：机器人下电成功后调用get_servo_state()为1伺服就绪状态，
        该函数只有伺服状态为3（运行状态）时调用生效。

        Returns:
            bool: 下电成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法下电")
            return False

        print("正在执行机器人下电...")
        result = nrc.set_servo_poweroff(self.socket_fd)

        if result == 0:  # SUCCESS
            print("机器人下电成功")
            return True
        else:
            print(f"机器人下电失败，错误代码: {result}")
            return False

    def robot_movej(self, target_pos, velocity, blending_radius):
        """
        发送一个阻塞式的关节运动指令 (MoveJ)

        增强的关节运动方法，包含伺服状态检查和安全验证。
        关节运动在3D打印中用于快速定位、避障和换层移动。
        程序会在此等待直到运动完成。

        Args:
            target_pos (list): 包含6个浮点数（X,Y,Z,A,B,C）的Python列表（笛卡尔坐标）
            velocity (float): 运动速度 (单位: mm/s)
            blending_radius (float): 路径平滑过渡的半径/等级 (pl 参数)

        Returns:
            bool: 运动成功返回True，失败返回False
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法执行关节运动")
            return False

        if len(target_pos) != 6:
            print("错误：目标位置必须包含6个坐标值 (X,Y,Z,A,B,C)")
            return False

        # 安全检查：验证伺服状态
        if not self.check_servo_ready_for_motion():
            print("✗ 伺服状态不适合执行运动，请先检查并使能伺服")
            return False

        print(f"正在执行关节移动至: {target_pos}")

        # 创建MoveCmd对象
        move_cmd = nrc.MoveCmd()

        # 创建VectorDouble对象并填充目标位置
        target_vector = nrc.VectorDouble()
        for pos in target_pos:
            target_vector.push_back(float(pos))

        # 填充MoveCmd对象的属性
        move_cmd.targetPosValue = target_vector
        move_cmd.velocity = float(velocity)
        move_cmd.pl = float(blending_radius)
        move_cmd.toolNum = 1  # 默认工具
        move_cmd.userNum = 1  # 默认坐标系

        # 执行关节运动
        result = nrc.robot_movej(self.socket_fd, move_cmd)

        if result == 0:  # SUCCESS
            print("关节运动执行成功")
            return True
        else:
            print(f"关节运动执行失败，错误代码: {result}")
            return False

    def get_servo_state(self):
        """
        获取伺服电机状态

        Returns:
            dict: 包含伺服状态信息的字典，失败返回None
            格式: {"enabled": bool, "status_code": int}
        """
        if self.socket_fd is None:
            print("错误：机器人未连接，无法获取伺服状态")
            return None

        # 创建VectorInt对象来接收伺服状态数据
        servo_status = nrc.VectorInt()

        # 调用API获取伺服状态
        result = nrc.get_servo_state(self.socket_fd, servo_status)

        if result == 0:  # SUCCESS
            if len(servo_status) > 0:
                status_code = servo_status[0]

                # 根据官方文档的伺服状态定义
                servo_descriptions = {
                    0: "停止状态",
                    1: "就绪状态",
                    2: "报警状态",
                    3: "运行状态"
                }

                description = servo_descriptions.get(status_code, f"未知状态({status_code})")
                enabled = status_code in [1, 3]  # 就绪或运行状态视为已使能

                return {
                    "enabled": enabled,
                    "status_code": status_code,
                    "description": description
                }
            else:
                print("获取伺服状态失败：返回数据为空")
                return None
        else:
            print(f"获取伺服状态失败，错误代码: {result}")
            return None

    def __del__(self):
        """
        析构函数，确保连接被正确关闭
        """
        self.disconnect()
