# src/robot/status.py
"""
状态监控专用模块

该模块专门负责机器人状态的查询和监控，包括运行状态、伺服状态等。

作者: Augment Agent
日期: 2025-07-15
"""

try:
    import nrc_interface as nrc
except ImportError:
    nrc = None


class StatusMonitor:
    """
    状态监控器
    
    专门负责机器人状态的查询和监控功能。
    """
    
    def __init__(self, connection_manager, error_handler):
        """
        初始化状态监控器
        
        Args:
            connection_manager: 连接管理器实例
            error_handler: 错误处理器实例
        """
        self.conn = connection_manager
        self.error_handler = error_handler
    
    def get_robot_running_state(self):
        """
        获取机器人运行状态
        
        这是3D打印应用中的关键功能，用于实时监控机器人状态。
        
        Returns:
            dict: 包含运行状态信息的字典，失败返回None
            格式: {"status": int, "description": str}
        """
        if self.conn.is_development_mode():
            return {
                "status": 0,
                "description": "停止状态"
            }
        
        if not self.conn.is_robot_connected():
            print("✗ 机器人未连接，无法获取运行状态")
            return None
        
        if nrc is None:
            print("⚠️ nrc_interface 不可用")
            return None
        
        try:
            # 创建VectorInt对象来接收状态数据
            status_data = nrc.VectorInt()
            
            # 调用API获取运行状态
            result = nrc.get_robot_running_state(self.conn.get_socket_fd(), status_data)
            
            if result == 0:  # SUCCESS
                if len(status_data) > 0:
                    status_code = status_data[0]
                    
                    # 状态码映射（根据INEXBOT文档）
                    status_descriptions = {
                        0: "停止状态",
                        1: "运行状态",
                        2: "暂停状态",
                        3: "错误状态",
                        4: "急停状态",
                        5: "准备状态"
                    }
                    
                    description = status_descriptions.get(status_code, f"未知状态({status_code})")
                    
                    return {
                        "status": status_code,
                        "description": description
                    }
                else:
                    print("✗ 获取运行状态失败：返回数据为空")
                    return None
            else:
                print(f"✗ 获取运行状态失败，错误代码: {result}")
                return None
                
        except Exception as e:
            print(f"✗ 获取运行状态异常: {e}")
            return None
    
    def get_servo_state(self):
        """
        获取伺服电机状态
        
        Returns:
            dict: 包含伺服状态信息的字典，失败返回None
            格式: {"enabled": bool, "status_code": int, "description": str}
        """
        if self.conn.is_development_mode():
            return {
                "enabled": True,
                "status_code": 1,
                "description": "就绪状态"
            }
        
        if not self.conn.is_robot_connected():
            print("✗ 机器人未连接，无法获取伺服状态")
            return None
        
        if nrc is None:
            print("⚠️ nrc_interface 不可用")
            return None
        
        try:
            # 创建VectorInt对象来接收伺服状态数据
            servo_status = nrc.VectorInt()
            
            # 调用API获取伺服状态
            result = nrc.get_servo_state(self.conn.get_socket_fd(), servo_status)
            
            if result == 0:  # SUCCESS
                if len(servo_status) > 0:
                    status_code = servo_status[0]
                    
                    # 根据官方文档的伺服状态定义
                    servo_descriptions = {
                        0: "停止状态",
                        1: "就绪状态",
                        2: "报警状态",
                        3: "运行状态"
                    }
                    
                    description = servo_descriptions.get(status_code, f"未知状态({status_code})")
                    enabled = status_code in [1, 3]  # 就绪或运行状态视为已使能
                    
                    return {
                        "enabled": enabled,
                        "status_code": status_code,
                        "description": description
                    }
                else:
                    print("✗ 获取伺服状态失败：返回数据为空")
                    return None
            else:
                print(f"✗ 获取伺服状态失败，错误代码: {result}")
                return None
                
        except Exception as e:
            print(f"✗ 获取伺服状态异常: {e}")
            return None
    
    def get_detailed_status(self):
        """
        获取详细的机器人状态信息
        
        Returns:
            dict: 包含详细状态信息的字典，失败返回None
        """
        running_state = self.get_robot_running_state()
        servo_state = self.get_servo_state()
        
        if running_state is None or servo_state is None:
            print("⚠️ 无法获取完整的状态信息")
            return None
        
        return {
            "running_state": running_state,
            "servo_state": servo_state,
            "connection_status": self.conn.get_connection_status(),
            "is_connected": self.conn.is_robot_connected(),
            "development_mode": self.conn.is_development_mode()
        }
    
    def monitor_status_continuously(self, duration=10, interval=1):
        """
        连续监控机器人状态
        
        Args:
            duration (int): 监控持续时间（秒）
            interval (int): 监控间隔（秒）
        """
        import time
        
        print(f"开始连续监控机器人状态，持续 {duration} 秒，间隔 {interval} 秒")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            status = self.get_detailed_status()
            if status:
                print(f"[{time.strftime('%H:%M:%S')}] "
                      f"运行状态: {status['running_state']['description']}, "
                      f"伺服状态: {status['servo_state']['description']}")
            else:
                print(f"[{time.strftime('%H:%M:%S')}] 无法获取状态信息")
            
            time.sleep(interval)
        
        print("状态监控结束")
    
    def check_system_health(self):
        """
        检查系统健康状态
        
        Returns:
            dict: 系统健康检查结果
        """
        health_report = {
            "overall_health": "unknown",
            "issues": [],
            "recommendations": []
        }
        
        # 检查连接状态
        if not self.conn.is_robot_connected():
            health_report["issues"].append("机器人未连接")
            health_report["recommendations"].append("检查网络连接和机器人电源")
        
        # 检查运行状态
        running_state = self.get_robot_running_state()
        if running_state:
            if running_state["status"] == 3:  # 错误状态
                health_report["issues"].append("机器人处于错误状态")
                health_report["recommendations"].append("调用clear_error()清除错误")
            elif running_state["status"] == 4:  # 急停状态
                health_report["issues"].append("机器人处于急停状态")
                health_report["recommendations"].append("检查急停按钮和安全系统")
        
        # 检查伺服状态
        servo_state = self.get_servo_state()
        if servo_state:
            if servo_state["status_code"] == 2:  # 报警状态
                health_report["issues"].append("伺服处于报警状态")
                health_report["recommendations"].append("清除伺服错误并检查硬件")
        
        # 确定整体健康状态
        if len(health_report["issues"]) == 0:
            health_report["overall_health"] = "healthy"
        elif len(health_report["issues"]) <= 2:
            health_report["overall_health"] = "warning"
        else:
            health_report["overall_health"] = "critical"
        
        return health_report
    
    def print_status_summary(self):
        """
        打印状态摘要
        """
        print("\n" + "="*50)
        print("           机器人状态摘要")
        print("="*50)
        
        status = self.get_detailed_status()
        if status:
            print(f"连接状态: {'已连接' if status['is_connected'] else '未连接'}")
            print(f"开发模式: {'是' if status['development_mode'] else '否'}")
            print(f"运行状态: {status['running_state']['description']}")
            print(f"伺服状态: {status['servo_state']['description']}")
            print(f"伺服使能: {'是' if status['servo_state']['enabled'] else '否'}")
        else:
            print("无法获取状态信息")
        
        # 健康检查
        health = self.check_system_health()
        print(f"系统健康: {health['overall_health']}")
        
        if health["issues"]:
            print("\n发现的问题:")
            for issue in health["issues"]:
                print(f"  - {issue}")
        
        if health["recommendations"]:
            print("\n建议:")
            for rec in health["recommendations"]:
                print(f"  - {rec}")
        
        print("="*50)
