#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行脚本

提供统一的测试运行入口，支持不同类型的测试。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))


def run_unit_tests():
    """运行单元测试"""
    print("="*60)
    print("运行单元测试")
    print("="*60)
    
    test_dir = project_root / "test" / "unit"
    test_files = list(test_dir.glob("test_*.py"))
    
    if not test_files:
        print("未找到单元测试文件")
        return False
    
    success = True
    for test_file in test_files:
        print(f"\n运行测试文件: {test_file.name}")
        try:
            result = subprocess.run([
                sys.executable, str(test_file)
            ], cwd=str(project_root), capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✓ {test_file.name} 通过")
            else:
                print(f"✗ {test_file.name} 失败")
                print("错误输出:")
                print(result.stderr)
                success = False
        except Exception as e:
            print(f"✗ 运行 {test_file.name} 时发生异常: {e}")
            success = False
    
    return success


def run_integration_tests():
    """运行集成测试"""
    print("="*60)
    print("运行集成测试")
    print("="*60)
    
    test_dir = project_root / "test" / "integration"
    test_files = list(test_dir.glob("test_*.py"))
    
    if not test_files:
        print("未找到集成测试文件")
        return False
    
    success = True
    for test_file in test_files:
        print(f"\n运行测试文件: {test_file.name}")
        try:
            result = subprocess.run([
                sys.executable, str(test_file)
            ], cwd=str(project_root))
            
            if result.returncode == 0:
                print(f"✓ {test_file.name} 通过")
            else:
                print(f"✗ {test_file.name} 失败")
                success = False
        except Exception as e:
            print(f"✗ 运行 {test_file.name} 时发生异常: {e}")
            success = False
    
    return success


def list_manual_tests():
    """列出手动测试"""
    print("="*60)
    print("手动测试文件（需要硬件连接）")
    print("="*60)
    
    test_dir = project_root / "test" / "manual"
    test_files = list(test_dir.glob("test_*.py"))
    
    if not test_files:
        print("未找到手动测试文件")
        return
    
    for test_file in test_files:
        print(f"📋 {test_file.name}")
        print(f"   路径: {test_file}")
        print(f"   运行: python {test_file}")
        print()


def run_development_tests():
    """运行开发环境测试（无需硬件）"""
    print("="*60)
    print("运行开发环境测试（无需硬件连接）")
    print("="*60)
    
    # 运行单元测试
    unit_success = run_unit_tests()
    
    print("\n" + "="*60)
    
    # 运行集成测试
    integration_success = run_integration_tests()
    
    print("\n" + "="*60)
    print("测试总结")
    print("="*60)
    
    if unit_success and integration_success:
        print("✓ 所有开发环境测试通过")
        return True
    else:
        print("✗ 部分测试失败")
        return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="测试运行脚本")
    parser.add_argument(
        "test_type", 
        choices=["unit", "integration", "manual", "dev", "all"],
        help="测试类型"
    )
    
    args = parser.parse_args()
    
    if args.test_type == "unit":
        success = run_unit_tests()
    elif args.test_type == "integration":
        success = run_integration_tests()
    elif args.test_type == "manual":
        list_manual_tests()
        success = True
    elif args.test_type == "dev":
        success = run_development_tests()
    elif args.test_type == "all":
        print("运行所有自动化测试...")
        success = run_development_tests()
        print("\n")
        list_manual_tests()
    else:
        print("未知的测试类型")
        success = False
    
    if args.test_type != "manual":
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
