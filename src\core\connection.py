# src/core/connection.py
"""
连接管理模块

该模块负责与INEXBOT机器人的连接管理，包括连接建立、断开和状态检查。
在开发环境下优雅处理连接失败的情况。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
# 添加lib目录到Python路径
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
except ImportError:
    print("⚠️ 开发模式：nrc_interface 模块未找到，这在开发环境下是正常的")
    nrc = None


class ConnectionManager:
    """
    连接管理器
    
    负责管理与INEXBOT机器人的连接，在开发环境下优雅处理连接失败。
    """
    
    def __init__(self, ip, port):
        """
        初始化连接管理器
        
        Args:
            ip (str): 机器人的IP地址
            port (int): 机器人的通信端口
        """
        self.ip = ip
        self.port = port
        self.socket_fd = None
        self.is_connected = False
        self._development_mode = False
    
    def connect(self):
        """
        连接机器人
        
        在开发环境下优雅处理连接失败，不会导致程序崩溃。
        
        Returns:
            bool: 连接成功返回True，失败返回False
        """
        if nrc is None:
            return self._handle_development_mode("nrc_interface 模块未导入")
        
        try:
            print(f"正在连接机器人 {self.ip}:{self.port}...")
            result = nrc.connect_robot(self.ip, str(self.port))
            
            if result < 0:
                return self._handle_connection_failure(f"连接失败，错误代码: {result}")
            
            self.socket_fd = result
            self.is_connected = True
            print(f"✓ 机器人连接成功！套接字FD: {self.socket_fd}")
            return True
            
        except Exception as e:
            return self._handle_connection_failure(f"连接异常: {e}")
    
    def disconnect(self):
        """
        断开与机器人的连接
        """
        if self._development_mode:
            print("💡 开发模式：模拟断开连接")
            self.is_connected = False
            return
        
        if self.socket_fd is not None and self.is_connected and nrc is not None:
            try:
                print("正在断开机器人连接...")
                nrc.disconnect_robot(self.socket_fd)
                self.is_connected = False
                self.socket_fd = None
                print("✓ 机器人连接已断开")
            except Exception as e:
                print(f"⚠️ 断开连接时发生异常: {e}")
        else:
            print("💡 机器人未连接或已断开")
    
    def get_connection_status(self):
        """
        获取连接状态
        
        Returns:
            int: 连接状态值，开发模式下返回模拟值
        """
        if self._development_mode:
            return 1 if self.is_connected else -1
        
        if self.socket_fd is not None and nrc is not None:
            try:
                return nrc.get_connection_status(self.socket_fd)
            except Exception as e:
                print(f"⚠️ 获取连接状态失败: {e}")
                return -1
        else:
            return -1
    
    def is_robot_connected(self):
        """
        检查机器人是否已连接
        
        Returns:
            bool: True表示已连接，False表示未连接
        """
        return self.is_connected and (self.socket_fd is not None or self._development_mode)
    
    def get_socket_fd(self):
        """
        获取套接字文件描述符
        
        Returns:
            int: 套接字文件描述符，开发模式下返回模拟值
        """
        if self._development_mode:
            return 999  # 模拟的socket_fd
        return self.socket_fd
    
    def _handle_connection_failure(self, error_msg):
        """
        处理连接失败
        
        Args:
            error_msg (str): 错误信息
            
        Returns:
            bool: 始终返回False，但会设置开发模式
        """
        print(f"⚠️ 连接失败: {error_msg}")
        print("💡 切换到开发模式：代码可以正常运行，但不会与真实硬件通信")
        print("💡 在实际测试时，请确保机器人硬件已连接并配置正确")
        
        self._development_mode = True
        self.is_connected = True  # 开发模式下模拟连接成功
        return False  # 返回False表示真实连接失败
    
    def _handle_development_mode(self, reason):
        """
        处理开发模式
        
        Args:
            reason (str): 进入开发模式的原因
            
        Returns:
            bool: 始终返回False
        """
        print(f"💡 开发模式: {reason}")
        print("💡 代码将正常运行，但不会与真实硬件通信")
        
        self._development_mode = True
        self.is_connected = True  # 开发模式下模拟连接成功
        return False
    
    def is_development_mode(self):
        """
        检查是否处于开发模式
        
        Returns:
            bool: True表示开发模式，False表示正常模式
        """
        return self._development_mode
    
    def __del__(self):
        """
        析构函数，确保连接被正确关闭
        """
        self.disconnect()
