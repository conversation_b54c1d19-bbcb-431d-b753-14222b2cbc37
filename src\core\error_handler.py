# src/core/error_handler.py
"""
错误处理模块

该模块提供统一的错误处理机制，包括错误清除、安全调用和开发环境友好的错误处理。

作者: Augment Agent
日期: 2025-07-15
"""

import sys
import os
# 添加lib目录到Python路径
lib_path = os.path.join(os.path.dirname(__file__), '..', '..', 'lib')
if lib_path not in sys.path:
    sys.path.append(lib_path)

try:
    import nrc_interface as nrc
except ImportError:
    nrc = None


class ErrorHandler:
    """
    统一错误处理器
    
    提供安全的API调用和错误处理机制，在开发环境下优雅处理硬件不可用的情况。
    """
    
    def __init__(self, connection_manager):
        """
        初始化错误处理器
        
        Args:
            connection_manager: 连接管理器实例
        """
        self.conn = connection_manager
    
    def safe_call(self, func, *args, **kwargs):
        """
        安全调用函数，处理连接失败和异常情况
        
        Args:
            func: 要调用的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            函数返回值，失败时返回None
        """
        if self.conn.is_development_mode():
            print(f"💡 开发模式：模拟调用 {func.__name__ if hasattr(func, '__name__') else 'function'}")
            return self._get_mock_result(func, *args, **kwargs)
        
        if not self.conn.is_robot_connected():
            print("⚠️ 机器人未连接，无法执行操作")
            return None
        
        try:
            return func(*args, **kwargs)
        except Exception as e:
            print(f"⚠️ 操作失败: {e}")
            if "连接" in str(e) or "connection" in str(e).lower():
                print("💡 可能是连接问题，请检查机器人状态")
            return None
    
    def clear_error(self):
        """
        清除机器人错误状态
        
        Returns:
            bool: 清除成功返回True，失败返回False
        """
        if self.conn.is_development_mode():
            print("💡 开发模式：模拟清除错误状态")
            return True
        
        if not self.conn.is_robot_connected():
            print("⚠️ 机器人未连接，无法清除错误")
            return False
        
        if nrc is None:
            print("⚠️ nrc_interface 不可用，无法清除错误")
            return False
        
        try:
            print("正在清除机器人错误状态...")
            result = nrc.clear_error(self.conn.get_socket_fd())
            
            if result == 0:  # SUCCESS
                print("✓ 机器人错误状态已清除")
                return True
            else:
                print(f"✗ 清除错误状态失败，错误代码: {result}")
                return False
                
        except Exception as e:
            print(f"✗ 清除错误状态异常: {e}")
            return False
    
    def validate_operation_safety(self, operation_name, required_states=None):
        """
        验证操作安全性
        
        Args:
            operation_name (str): 操作名称
            required_states (list): 要求的伺服状态列表
            
        Returns:
            bool: 安全返回True，不安全返回False
        """
        if self.conn.is_development_mode():
            print(f"💡 开发模式：跳过 {operation_name} 安全检查")
            return True
        
        if not self.conn.is_robot_connected():
            print(f"✗ {operation_name} 失败：机器人未连接")
            return False
        
        # 如果指定了要求的状态，进行状态检查
        if required_states is not None:
            # 这里可以添加具体的状态检查逻辑
            # 暂时返回True，具体检查由各模块实现
            pass
        
        return True
    
    def handle_servo_alarm(self):
        """
        处理伺服报警状态
        
        Returns:
            bool: 处理成功返回True，失败返回False
        """
        print("⚠️ 检测到伺服报警状态，尝试清除错误...")
        
        if not self.clear_error():
            print("✗ 清除伺服错误失败")
            return False
        
        # 等待错误清除生效
        import time
        time.sleep(1)
        
        print("✓ 伺服错误清除完成")
        return True
    
    def _get_mock_result(self, func, *args, **kwargs):
        """
        获取模拟结果，用于开发模式
        
        Args:
            func: 函数对象
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        Returns:
            模拟的返回值
        """
        func_name = func.__name__ if hasattr(func, '__name__') else str(func)
        
        # 根据函数名返回合适的模拟值
        if 'get_servo_state' in func_name:
            return [1]  # 模拟就绪状态
        elif 'get_robot_running_state' in func_name:
            return [0]  # 模拟停止状态
        elif 'get_current_position' in func_name:
            return [0.0, 0.0, 0.0, 0.0, 0.0, 0.0]  # 模拟位置
        elif 'set_servo_state' in func_name:
            return 0  # 模拟成功
        elif 'robot_movel' in func_name or 'robot_movej' in func_name:
            return 0  # 模拟运动成功
        elif 'set_speed' in func_name:
            return 0  # 模拟设置成功
        elif 'get_speed' in func_name:
            return [50]  # 模拟当前速度
        else:
            return 0  # 默认模拟成功
    
    @staticmethod
    def format_error_message(operation, error_code=None, error_msg=None):
        """
        格式化错误消息
        
        Args:
            operation (str): 操作名称
            error_code (int): 错误代码
            error_msg (str): 错误消息
            
        Returns:
            str: 格式化的错误消息
        """
        if error_code is not None:
            return f"✗ {operation} 失败，错误代码: {error_code}"
        elif error_msg is not None:
            return f"✗ {operation} 失败: {error_msg}"
        else:
            return f"✗ {operation} 失败"
    
    @staticmethod
    def format_success_message(operation, details=None):
        """
        格式化成功消息
        
        Args:
            operation (str): 操作名称
            details (str): 详细信息
            
        Returns:
            str: 格式化的成功消息
        """
        if details:
            return f"✓ {operation} 成功: {details}"
        else:
            return f"✓ {operation} 成功"
